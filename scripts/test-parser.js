/**
 * 简单的解析器测试脚本
 * 用于验证解析器系统是否正常工作
 */

const { parseProductFromUrl } = require('../lib/submit/url-parser')

async function testParser() {
  console.log('🧪 测试解析器系统...\n')

  const testUrls = [
    'https://github.com/vercel/next.js',
    'https://supabase.com',
    'https://linear.app',
    'https://example.com'
  ]

  for (const url of testUrls) {
    console.log(`📝 测试URL: ${url}`)
    
    try {
      const startTime = Date.now()
      const result = await parseProductFromUrl(url)
      const duration = Date.now() - startTime
      
      console.log(`✅ 成功 (${duration}ms)`)
      console.log(`   名称: ${result.name || '未知'}`)
      console.log(`   标语: ${result.tagline || '无'}`)
      console.log(`   分类: ${result.category || '无'}`)
      console.log(`   标签: ${result.tags?.join(', ') || '无'}`)
      
    } catch (error) {
      console.log(`❌ 失败: ${error.message}`)
    }
    
    console.log('---')
  }
  
  console.log('✨ 测试完成!')
}

// 如果直接运行此脚本
if (require.main === module) {
  testParser().catch(console.error)
}

module.exports = { testParser }
