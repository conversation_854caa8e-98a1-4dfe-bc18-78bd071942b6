// 简单的解析器测试文件
// 注意：这些测试需要在实际环境中运行，因为涉及网络请求

import { BasicParser } from '../basic-parser'
import { AutoParser } from '../auto-parser'
import { ParserFactory } from '../index'
import { ParseStrategy } from '../types'

describe('Parser System', () => {
  describe('BasicParser', () => {
    const parser = new BasicParser()

    test('should always be available', () => {
      expect(parser.isAvailable()).toBe(true)
    })

    test('should parse basic info from URL', async () => {
      const result = await parser.parse('https://example-app.com')
      
      expect(result.success).toBe(true)
      expect(result.data).toBeDefined()
      expect(result.data?.name).toBe('Example App')
      expect(result.source).toBe('basic')
    })

    test('should handle invalid URLs gracefully', async () => {
      const result = await parser.parse('invalid-url')
      
      expect(result.success).toBe(true) // BasicParser总是成功
      expect(result.data?.name).toBe('Unknown Product')
    })
  })

  describe('AutoParser', () => {
    test('should create with default config', () => {
      const parser = new AutoParser()
      expect(parser).toBeDefined()
    })

    test('should use fallback strategy by default', async () => {
      const parser = new AutoParser({
        strategy: ParseStrategy.FALLBACK,
        timeout: 5000,
        maxRetries: 0,
        enabledParsers: ['basic'],
        priorities: [{ parser: 'basic', weight: 100 }]
      })

      const result = await parser.parse('https://test.com')
      expect(result.success).toBe(true)
      expect(result.source).toBe('basic')
    })

    test('should get available parsers', () => {
      const parser = new AutoParser({
        strategy: ParseStrategy.FALLBACK,
        timeout: 5000,
        maxRetries: 0,
        enabledParsers: ['basic'],
        priorities: [{ parser: 'basic', weight: 100 }]
      })

      const available = parser.getAvailableParsers()
      expect(available).toContain('basic')
    })
  })

  describe('ParserFactory', () => {
    test('should create default parser instance', () => {
      const parser = ParserFactory.getInstance('default')
      expect(parser).toBeDefined()
    })

    test('should create fast parser instance', () => {
      const parser = ParserFactory.getInstance('fast')
      expect(parser).toBeDefined()
    })

    test('should reuse instances', () => {
      const parser1 = ParserFactory.getInstance('default')
      const parser2 = ParserFactory.getInstance('default')
      expect(parser1).toBe(parser2)
    })

    test('should create optimized instance for URL', () => {
      const parser = ParserFactory.getOptimizedInstance('https://github.com/user/repo')
      expect(parser).toBeDefined()
    })

    test('should clear instances', () => {
      ParserFactory.getInstance('default')
      ParserFactory.clearInstances()
      
      const parser1 = ParserFactory.getInstance('default')
      const parser2 = ParserFactory.getInstance('default')
      expect(parser1).toBe(parser2) // 应该是新创建的相同实例
    })
  })

  describe('URL Pattern Matching', () => {
    test('should prioritize Jina for GitHub URLs', () => {
      const parser = ParserFactory.getOptimizedInstance('https://github.com/user/repo')
      // 这里我们无法直接测试内部优先级，但可以确保解析器被创建
      expect(parser).toBeDefined()
    })

    test('should prioritize Firecrawl for SaaS URLs', () => {
      const parser = ParserFactory.getOptimizedInstance('https://myapp.app')
      expect(parser).toBeDefined()
    })

    test('should handle docs URLs', () => {
      const parser = ParserFactory.getOptimizedInstance('https://docs.example.com')
      expect(parser).toBeDefined()
    })
  })
})

// 集成测试（需要真实网络环境）
describe('Integration Tests', () => {
  // 这些测试只在有网络连接时运行
  const hasNetwork = process.env.NODE_ENV !== 'test'

  test.skipIf(!hasNetwork)('should parse real URL with basic parser', async () => {
    const parser = new BasicParser()
    const result = await parser.parse('https://github.com')
    
    expect(result.success).toBe(true)
    expect(result.data?.name).toBe('Github')
  })

  test.skipIf(!hasNetwork)('should handle timeout gracefully', async () => {
    const parser = new AutoParser({
      strategy: ParseStrategy.FALLBACK,
      timeout: 1, // 1ms超时，必定失败
      maxRetries: 0,
      enabledParsers: ['basic'],
      priorities: [{ parser: 'basic', weight: 100 }]
    })

    const result = await parser.parse('https://example.com')
    // BasicParser不进行网络请求，所以应该成功
    expect(result.success).toBe(true)
  })
})

// 性能测试
describe('Performance Tests', () => {
  test('should complete parsing within reasonable time', async () => {
    const parser = ParserFactory.getInstance('fast')
    const startTime = Date.now()
    
    const result = await parser.parse('https://example.com')
    const duration = Date.now() - startTime
    
    expect(duration).toBeLessThan(10000) // 应该在10秒内完成
    expect(result.duration).toBeGreaterThan(0)
  })

  test('should handle multiple concurrent requests', async () => {
    const parser = ParserFactory.getInstance('fast')
    const urls = [
      'https://example1.com',
      'https://example2.com',
      'https://example3.com'
    ]

    const promises = urls.map(url => parser.parse(url))
    const results = await Promise.all(promises)

    results.forEach(result => {
      expect(result.success).toBe(true)
      expect(result.duration).toBeGreaterThan(0)
    })
  })
})
