// 导出所有解析器类型和接口
export * from './types'

// 导出解析器实现
export { JinaParser } from './jina-parser'
export { FirecrawlParser } from './firecrawl-parser'
export { BasicParser } from './basic-parser'
export { AutoParser } from './auto-parser'

// 导出配置
export * from './config'

// 便捷函数：创建预配置的解析器实例
/**
 * 创建一个快速解析器实例
 * 适用于需要快速响应的场景
 */
export async function createFastParser() {
  const { AutoParser } = await import('./auto-parser')
  const { PARSER_PRESETS } = await import('./config')
  return new AutoParser(PARSER_PRESETS.FAST)
}

/**
 * 创建一个平衡解析器实例
 * 在速度和质量之间平衡，适用于大多数场景
 */
export async function createBalancedParser() {
  const { AutoParser } = await import('./auto-parser')
  const { PARSER_PRESETS } = await import('./config')
  return new AutoParser(PARSER_PRESETS.BALANCED)
}

/**
 * 创建一个高质量解析器实例
 * 优先获取详细信息，适用于重要的产品解析
 */
export async function createHighQualityParser() {
  const { AutoParser } = await import('./auto-parser')
  const { PARSER_PRESETS } = await import('./config')
  return new AutoParser(PARSER_PRESETS.HIGH_QUALITY)
}

/**
 * 创建一个综合解析器实例
 * 使用并行策略，合并多个解析器的结果
 */
export async function createComprehensiveParser() {
  const { AutoParser } = await import('./auto-parser')
  const { PARSER_PRESETS } = await import('./config')
  return new AutoParser(PARSER_PRESETS.COMPREHENSIVE)
}

/**
 * 创建默认解析器实例
 * 根据当前环境自动选择合适的配置
 */
export async function createDefaultParser() {
  const { AutoParser } = await import('./auto-parser')
  const { getDefaultParserConfig } = await import('./config')
  return new AutoParser(getDefaultParserConfig())
}

/**
 * 为特定URL创建推荐的解析器实例
 * 根据URL特征自动选择最适合的配置
 */
export async function createRecommendedParser(url: string) {
  const { AutoParser } = await import('./auto-parser')
  const { getRecommendedConfig } = await import('./config')
  return new AutoParser(getRecommendedConfig(url))
}

// 解析器工厂类
export class ParserFactory {
  private static instances = new Map<string, AutoParser>()

  /**
   * 获取单例解析器实例
   */
  static async getInstance(type: 'fast' | 'balanced' | 'high-quality' | 'comprehensive' | 'default' = 'default'): Promise<AutoParser> {
    if (!this.instances.has(type)) {
      let parser: AutoParser
      switch (type) {
        case 'fast':
          parser = await createFastParser()
          break
        case 'balanced':
          parser = await createBalancedParser()
          break
        case 'high-quality':
          parser = await createHighQualityParser()
          break
        case 'comprehensive':
          parser = await createComprehensiveParser()
          break
        case 'default':
        default:
          parser = await createDefaultParser()
          break
      }
      this.instances.set(type, parser)
    }
    return this.instances.get(type)!
  }

  /**
   * 清除所有缓存的实例
   */
  static clearInstances() {
    this.instances.clear()
  }

  /**
   * 获取为特定URL优化的解析器实例
   */
  static async getOptimizedInstance(url: string): Promise<AutoParser> {
    try {
      const cacheKey = `optimized-${new URL(url).hostname}`

      if (!this.instances.has(cacheKey)) {
        const parser = await createRecommendedParser(url)
        this.instances.set(cacheKey, parser)
      }

      return this.instances.get(cacheKey)!
    } catch (error) {
      // 如果URL解析失败，使用默认解析器
      return this.getInstance('default')
    }
  }
}
