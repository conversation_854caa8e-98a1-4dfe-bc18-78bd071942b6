// 导出所有解析器类型和接口
export * from './types'

// 导出解析器实现
export { JinaParser } from './jina-parser'
export { FirecrawlParser } from './firecrawl-parser'
export { BasicParser } from './basic-parser'
export { AutoParser } from './auto-parser'

// 导出配置
export * from './config'

// 便捷函数：创建预配置的解析器实例
import { AutoParser } from './auto-parser'
import { PARSER_PRESETS, getDefaultParserConfig, getRecommendedConfig } from './config'

/**
 * 创建一个快速解析器实例
 * 适用于需要快速响应的场景
 */
export function createFastParser() {
  return new AutoParser(PARSER_PRESETS.FAST)
}

/**
 * 创建一个平衡解析器实例
 * 在速度和质量之间平衡，适用于大多数场景
 */
export function createBalancedParser() {
  return new AutoParser(PARSER_PRESETS.BALANCED)
}

/**
 * 创建一个高质量解析器实例
 * 优先获取详细信息，适用于重要的产品解析
 */
export function createHighQualityParser() {
  return new AutoParser(PARSER_PRESETS.HIGH_QUALITY)
}

/**
 * 创建一个综合解析器实例
 * 使用并行策略，合并多个解析器的结果
 */
export function createComprehensiveParser() {
  return new AutoParser(PARSER_PRESETS.COMPREHENSIVE)
}

/**
 * 创建默认解析器实例
 * 根据当前环境自动选择合适的配置
 */
export function createDefaultParser() {
  return new AutoParser(getDefaultParserConfig())
}

/**
 * 为特定URL创建推荐的解析器实例
 * 根据URL特征自动选择最适合的配置
 */
export function createRecommendedParser(url: string) {
  return new AutoParser(getRecommendedConfig(url))
}

// 解析器工厂类
export class ParserFactory {
  private static instances = new Map<string, AutoParser>()

  /**
   * 获取单例解析器实例
   */
  static getInstance(type: 'fast' | 'balanced' | 'high-quality' | 'comprehensive' | 'default' = 'default'): AutoParser {
    if (!this.instances.has(type)) {
      switch (type) {
        case 'fast':
          this.instances.set(type, createFastParser())
          break
        case 'balanced':
          this.instances.set(type, createBalancedParser())
          break
        case 'high-quality':
          this.instances.set(type, createHighQualityParser())
          break
        case 'comprehensive':
          this.instances.set(type, createComprehensiveParser())
          break
        case 'default':
        default:
          this.instances.set(type, createDefaultParser())
          break
      }
    }
    return this.instances.get(type)!
  }

  /**
   * 清除所有缓存的实例
   */
  static clearInstances() {
    this.instances.clear()
  }

  /**
   * 获取为特定URL优化的解析器实例
   */
  static getOptimizedInstance(url: string): AutoParser {
    const cacheKey = `optimized-${new URL(url).hostname}`
    
    if (!this.instances.has(cacheKey)) {
      this.instances.set(cacheKey, createRecommendedParser(url))
    }
    
    return this.instances.get(cacheKey)!
  }
}
