import { 
  ProductParser, 
  ParseResult, 
  ParsedProductInfo, 
  AutoParserConfig, 
  ParseStrategy,
  ParserPriority 
} from './types'
import { JinaParser } from './jina-parser'
import { FirecrawlParser } from './firecrawl-parser'
import { BasicParser } from './basic-parser'

export class AutoParser {
  private parsers: Map<string, ProductParser> = new Map()
  private config: AutoParserConfig

  constructor(config?: Partial<AutoParserConfig>) {
    // 默认配置
    this.config = {
      strategy: ParseStrategy.FALLBACK,
      timeout: 30000,
      maxRetries: 2,
      enabledParsers: ['firecrawl', 'jina', 'basic'],
      priorities: [
        {
          parser: 'firecrawl',
          weight: 100,
          conditions: {
            domains: ['app.', 'saas.', 'platform.'] // 对SaaS产品优先使用Firecrawl
          }
        },
        {
          parser: 'jina',
          weight: 80,
          conditions: {
            patterns: [/github\.com/, /gitlab\.com/] // 对代码仓库优先使用Jina
          }
        },
        {
          parser: 'basic',
          weight: 10 // 基础解析器作为最后的备选
        }
      ],
      ...config
    }

    // 初始化解析器
    this.initializeParsers()
  }

  private initializeParsers() {
    const allParsers = [
      new JinaParser(),
      new FirecrawlParser(),
      new BasicParser()
    ]

    allParsers.forEach(parser => {
      if (this.config.enabledParsers.includes(parser.name)) {
        this.parsers.set(parser.name, parser)
      }
    })
  }

  async parse(url: string): Promise<ParseResult> {
    switch (this.config.strategy) {
      case ParseStrategy.FIRST_SUCCESS:
        return this.parseFirstSuccess(url)
      case ParseStrategy.BEST_RESULT:
        return this.parseBestResult(url)
      case ParseStrategy.PARALLEL:
        return this.parseParallel(url)
      case ParseStrategy.FALLBACK:
      default:
        return this.parseFallback(url)
    }
  }

  private async parseFirstSuccess(url: string): Promise<ParseResult> {
    const orderedParsers = this.getOrderedParsers(url)
    
    for (const parser of orderedParsers) {
      if (!parser.isAvailable()) continue
      
      const result = await parser.parse(url, { timeout: this.config.timeout })
      if (result.success) {
        return result
      }
    }

    return {
      success: false,
      error: 'All parsers failed',
      source: 'auto-parser',
      duration: 0
    }
  }

  private async parseBestResult(url: string): Promise<ParseResult> {
    const orderedParsers = this.getOrderedParsers(url)
    const results: ParseResult[] = []
    
    // 并行执行所有可用的解析器
    const promises = orderedParsers
      .filter(parser => parser.isAvailable())
      .map(parser => parser.parse(url, { timeout: this.config.timeout }))
    
    const allResults = await Promise.allSettled(promises)
    
    allResults.forEach(result => {
      if (result.status === 'fulfilled' && result.value.success) {
        results.push(result.value)
      }
    })

    if (results.length === 0) {
      return {
        success: false,
        error: 'No successful parsing results',
        source: 'auto-parser',
        duration: 0
      }
    }

    // 选择最佳结果（基于数据完整性评分）
    const bestResult = results.reduce((best, current) => {
      const bestScore = this.scoreResult(best)
      const currentScore = this.scoreResult(current)
      return currentScore > bestScore ? current : best
    })

    return bestResult
  }

  private async parseParallel(url: string): Promise<ParseResult> {
    const orderedParsers = this.getOrderedParsers(url)
    const startTime = Date.now()
    
    // 并行执行所有可用的解析器
    const promises = orderedParsers
      .filter(parser => parser.isAvailable())
      .map(parser => parser.parse(url, { timeout: this.config.timeout }))
    
    const allResults = await Promise.allSettled(promises)
    const successResults: ParseResult[] = []
    
    allResults.forEach(result => {
      if (result.status === 'fulfilled' && result.value.success) {
        successResults.push(result.value)
      }
    })

    if (successResults.length === 0) {
      return {
        success: false,
        error: 'No successful parsing results',
        source: 'auto-parser',
        duration: Date.now() - startTime
      }
    }

    // 合并所有成功的结果
    const mergedData = this.mergeResults(successResults.map(r => r.data!))
    
    return {
      success: true,
      data: mergedData,
      source: `auto-parser (${successResults.map(r => r.source).join(', ')})`,
      duration: Date.now() - startTime
    }
  }

  private async parseFallback(url: string): Promise<ParseResult> {
    const orderedParsers = this.getOrderedParsers(url)
    
    for (const parser of orderedParsers) {
      if (!parser.isAvailable()) continue
      
      let retries = 0
      while (retries <= this.config.maxRetries) {
        try {
          const result = await parser.parse(url, { timeout: this.config.timeout })
          if (result.success) {
            return result
          }
        } catch (error) {
          console.warn(`Parser ${parser.name} failed (attempt ${retries + 1}):`, error)
        }
        retries++
      }
    }

    return {
      success: false,
      error: 'All parsers failed after retries',
      source: 'auto-parser',
      duration: 0
    }
  }

  private getOrderedParsers(url: string): ProductParser[] {
    const parserScores = new Map<string, number>()
    
    // 计算每个解析器的优先级分数
    this.config.priorities.forEach(priority => {
      const parser = this.parsers.get(priority.parser)
      if (!parser) return
      
      let score = priority.weight
      
      // 检查域名条件
      if (priority.conditions?.domains) {
        const matchesDomain = priority.conditions.domains.some(domain => 
          url.toLowerCase().includes(domain.toLowerCase())
        )
        if (matchesDomain) score += 50
      }
      
      // 检查模式条件
      if (priority.conditions?.patterns) {
        const matchesPattern = priority.conditions.patterns.some(pattern => 
          pattern.test(url)
        )
        if (matchesPattern) score += 50
      }
      
      parserScores.set(priority.parser, score)
    })
    
    // 按分数排序
    const sortedParsers = Array.from(this.parsers.values())
      .filter(parser => parserScores.has(parser.name))
      .sort((a, b) => (parserScores.get(b.name) || 0) - (parserScores.get(a.name) || 0))
    
    return sortedParsers
  }

  private scoreResult(result: ParseResult): number {
    if (!result.success || !result.data) return 0
    
    const data = result.data
    let score = 0
    
    // 基础字段评分
    if (data.name) score += 20
    if (data.tagline) score += 15
    if (data.description) score += 25
    if (data.logoUrl) score += 10
    if (data.coverImageUrl) score += 10
    if (data.category) score += 10
    
    // 额外字段评分
    if (data.tags && data.tags.length > 0) score += 5
    if (data.features && data.features.length > 0) score += 5
    if (data.price) score += 5
    
    // 内容质量评分
    if (data.description && data.description.length > 100) score += 10
    if (data.tags && data.tags.length >= 3) score += 5
    
    return score
  }

  private mergeResults(results: ParsedProductInfo[]): ParsedProductInfo {
    const merged: ParsedProductInfo = {}
    
    // 选择最佳的每个字段
    results.forEach(result => {
      if (result.name && (!merged.name || result.name.length > merged.name.length)) {
        merged.name = result.name
      }
      if (result.tagline && (!merged.tagline || result.tagline.length > merged.tagline.length)) {
        merged.tagline = result.tagline
      }
      if (result.description && (!merged.description || result.description.length > merged.description.length)) {
        merged.description = result.description
      }
      if (result.logoUrl && !merged.logoUrl) {
        merged.logoUrl = result.logoUrl
      }
      if (result.coverImageUrl && !merged.coverImageUrl) {
        merged.coverImageUrl = result.coverImageUrl
      }
      if (result.category && !merged.category) {
        merged.category = result.category
      }
      if (result.price && !merged.price) {
        merged.price = result.price
      }
    })
    
    // 合并标签和特性
    const allTags = new Set<string>()
    const allFeatures = new Set<string>()
    
    results.forEach(result => {
      result.tags?.forEach(tag => allTags.add(tag))
      result.features?.forEach(feature => allFeatures.add(feature))
    })
    
    merged.tags = Array.from(allTags)
    merged.features = Array.from(allFeatures)
    
    return merged
  }

  // 获取可用的解析器列表
  getAvailableParsers(): string[] {
    return Array.from(this.parsers.values())
      .filter(parser => parser.isAvailable())
      .map(parser => parser.name)
  }

  // 更新配置
  updateConfig(config: Partial<AutoParserConfig>) {
    this.config = { ...this.config, ...config }
    this.initializeParsers() // 重新初始化解析器
  }
}
