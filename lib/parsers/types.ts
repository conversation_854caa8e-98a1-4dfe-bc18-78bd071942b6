// 解析器返回的产品信息接口
export interface ParsedProductInfo {
  name?: string
  tagline?: string
  description?: string
  logoUrl?: string
  coverImageUrl?: string
  category?: string
  tags?: string[]
  price?: string
  features?: string[]
  screenshots?: string[]
}

// 解析器配置接口
export interface ParserConfig {
  timeout?: number
  retries?: number
  userAgent?: string
  headers?: Record<string, string>
}

// 解析器结果接口
export interface ParseResult {
  success: boolean
  data?: ParsedProductInfo
  error?: string
  source: string // 解析器名称
  duration: number // 解析耗时（毫秒）
}

// 解析器接口
export interface ProductParser {
  name: string
  parse(url: string, config?: ParserConfig): Promise<ParseResult>
  isAvailable(): boolean // 检查解析器是否可用（API密钥等）
}

// 解析器优先级配置
export interface ParserPriority {
  parser: string
  weight: number // 权重，数字越大优先级越高
  conditions?: {
    domains?: string[] // 特定域名使用此解析器
    patterns?: RegExp[] // URL模式匹配
  }
}

// 解析策略枚举
export enum ParseStrategy {
  FIRST_SUCCESS = 'first_success', // 第一个成功的结果
  BEST_RESULT = 'best_result', // 选择最佳结果
  PARALLEL = 'parallel', // 并行解析，合并结果
  FALLBACK = 'fallback' // 按优先级依次尝试
}

// 解析器管理器配置
export interface AutoParserConfig {
  strategy: ParseStrategy
  timeout: number
  maxRetries: number
  priorities: ParserPriority[]
  enabledParsers: string[]
}
