import { AutoParserConfig, ParseStrategy } from './types'

// 预设配置
export const PARSER_PRESETS = {
  // 快速模式：优先使用基础解析器，快速返回结果
  FAST: {
    strategy: ParseStrategy.FIRST_SUCCESS,
    timeout: 10000,
    maxRetries: 0,
    enabledParsers: ['basic', 'jina'],
    priorities: [
      { parser: 'basic', weight: 100 },
      { parser: 'jina', weight: 50 }
    ]
  } as AutoParserConfig,

  // 平衡模式：在速度和质量之间平衡
  BALANCED: {
    strategy: ParseStrategy.FALLBACK,
    timeout: 20000,
    maxRetries: 1,
    enabledParsers: ['jina', 'firecrawl', 'basic'],
    priorities: [
      {
        parser: 'jina',
        weight: 90,
        conditions: {
          patterns: [/github\.com/, /gitlab\.com/, /\.dev/]
        }
      },
      {
        parser: 'firecrawl',
        weight: 80,
        conditions: {
          domains: ['.app', '.io', 'saas.', 'platform.']
        }
      },
      { parser: 'basic', weight: 10 }
    ]
  } as AutoParserConfig,

  // 高质量模式：优先使用高级解析器，获取最详细的信息
  HIGH_QUALITY: {
    strategy: ParseStrategy.BEST_RESULT,
    timeout: 45000,
    maxRetries: 2,
    enabledParsers: ['firecrawl', 'jina', 'basic'],
    priorities: [
      {
        parser: 'firecrawl',
        weight: 100,
        conditions: {
          domains: ['.app', '.io', 'saas.', 'platform.', 'app.']
        }
      },
      {
        parser: 'jina',
        weight: 90,
        conditions: {
          patterns: [/github\.com/, /gitlab\.com/, /docs\./, /\.dev/]
        }
      },
      { parser: 'basic', weight: 20 }
    ]
  } as AutoParserConfig,

  // 并行模式：同时使用多个解析器，合并结果
  COMPREHENSIVE: {
    strategy: ParseStrategy.PARALLEL,
    timeout: 30000,
    maxRetries: 1,
    enabledParsers: ['firecrawl', 'jina', 'basic'],
    priorities: [
      { parser: 'firecrawl', weight: 100 },
      { parser: 'jina', weight: 90 },
      { parser: 'basic', weight: 80 }
    ]
  } as AutoParserConfig
}

// 根据环境获取默认配置
export function getDefaultParserConfig(): AutoParserConfig {
  const env = process.env.NODE_ENV

  switch (env) {
    case 'development':
      return PARSER_PRESETS.BALANCED
    case 'production':
      return PARSER_PRESETS.HIGH_QUALITY
    case 'test':
      return PARSER_PRESETS.FAST
    default:
      return PARSER_PRESETS.BALANCED
  }
}

// 根据URL特征推荐配置
export function getRecommendedConfig(url: string): AutoParserConfig {
  const lowerUrl = url.toLowerCase()

  // GitHub/GitLab等代码仓库
  if (lowerUrl.includes('github.com') || lowerUrl.includes('gitlab.com')) {
    return {
      ...PARSER_PRESETS.BALANCED,
      priorities: [
        {
          parser: 'jina',
          weight: 100,
          conditions: { patterns: [/github\.com/, /gitlab\.com/] }
        },
        { parser: 'basic', weight: 50 }
      ]
    }
  }

  // SaaS产品
  if (lowerUrl.includes('.app') || lowerUrl.includes('.io') || lowerUrl.includes('saas')) {
    return {
      ...PARSER_PRESETS.HIGH_QUALITY,
      priorities: [
        {
          parser: 'firecrawl',
          weight: 100,
          conditions: { domains: ['.app', '.io', 'saas.'] }
        },
        { parser: 'jina', weight: 70 },
        { parser: 'basic', weight: 30 }
      ]
    }
  }

  // 文档站点
  if (lowerUrl.includes('docs.') || lowerUrl.includes('documentation')) {
    return {
      ...PARSER_PRESETS.BALANCED,
      priorities: [
        {
          parser: 'jina',
          weight: 90,
          conditions: { patterns: [/docs\./, /documentation/] }
        },
        { parser: 'firecrawl', weight: 70 },
        { parser: 'basic', weight: 40 }
      ]
    }
  }

  // 默认使用平衡配置
  return PARSER_PRESETS.BALANCED
}

// 验证配置
export function validateConfig(config: AutoParserConfig): boolean {
  // 检查必需字段
  if (!config.strategy || !config.enabledParsers || !config.priorities) {
    return false
  }

  // 检查启用的解析器是否在优先级列表中
  const priorityParsers = config.priorities.map(p => p.parser)
  const hasValidParsers = config.enabledParsers.every(parser => 
    priorityParsers.includes(parser)
  )

  if (!hasValidParsers) {
    return false
  }

  // 检查超时时间
  if (config.timeout <= 0 || config.timeout > 120000) {
    return false
  }

  return true
}

// 合并配置
export function mergeConfigs(base: AutoParserConfig, override: Partial<AutoParserConfig>): AutoParserConfig {
  return {
    ...base,
    ...override,
    priorities: override.priorities || base.priorities
  }
}
