import { ProductParser, ParseResult, ParsedProductInfo, ParserConfig } from './types'

export class BasicParser implements ProductParser {
  name = 'basic'

  isAvailable(): boolean {
    return true // 基础解析器总是可用
  }

  async parse(url: string, config?: ParserConfig): Promise<ParseResult> {
    const startTime = Date.now()
    
    try {
      // 基础解析器只从URL生成基本信息，不进行网络请求
      const parsedInfo = this.extractBasicInfo(url)

      return {
        success: true,
        data: parsedInfo,
        source: this.name,
        duration: Date.now() - startTime
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        source: this.name,
        duration: Date.now() - startTime
      }
    }
  }

  private extractBasicInfo(url: string): ParsedProductInfo {
    try {
      const normalizedUrl = this.normalizeUrl(url)
      const urlObj = new URL(normalizedUrl)
      const domain = urlObj.hostname.replace('www.', '')
      
      // 从域名生成产品名称
      const domainParts = domain.split('.')
      const productName = domainParts[0]
        .split('-')
        .map(part => part.charAt(0).toUpperCase() + part.slice(1))
        .join(' ')

      // 生成基础标语
      const tagline = `Discover ${productName} - An innovative solution`

      // 生成基础描述
      const description = `${productName} is a modern platform that helps users achieve their goals. Visit ${domain} to learn more about what makes it special.`

      // 根据域名推断可能的分类
      const category = this.inferCategoryFromDomain(domain)

      // 生成基础标签
      const tags = this.generateBasicTags(domain)

      return {
        name: productName,
        tagline,
        description,
        category,
        tags
      }
    } catch (error) {
      return {
        name: 'Unknown Product',
        tagline: 'A new product to discover',
        description: 'This product is waiting to be explored.'
      }
    }
  }

  private normalizeUrl(url: string): string {
    if (!url) return ''
    
    // Remove trailing slash
    url = url.replace(/\/$/, '')
    
    // Add https if no protocol
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = `https://${url}`
    }
    
    return url
  }

  private inferCategoryFromDomain(domain: string): string | undefined {
    const categoryKeywords = {
      'AI': ['ai', 'ml', 'neural', 'bot', 'gpt', 'llm'],
      'Developer Tools': ['api', 'dev', 'code', 'git', 'sdk', 'cli'],
      'Design': ['design', 'ui', 'ux', 'figma', 'sketch', 'creative'],
      'Productivity': ['task', 'todo', 'note', 'plan', 'organize', 'workflow'],
      'Marketing': ['market', 'seo', 'analytics', 'campaign', 'ads'],
      'E-commerce': ['shop', 'store', 'cart', 'pay', 'commerce', 'sell'],
      'Social': ['social', 'chat', 'community', 'connect', 'share'],
      'Finance': ['pay', 'bank', 'finance', 'money', 'crypto', 'invest'],
      'Health': ['health', 'fit', 'medical', 'wellness', 'care'],
      'Education': ['learn', 'edu', 'course', 'teach', 'study', 'academy']
    }

    const lowerDomain = domain.toLowerCase()
    
    for (const [category, keywords] of Object.entries(categoryKeywords)) {
      if (keywords.some(keyword => lowerDomain.includes(keyword))) {
        return category
      }
    }
    
    return undefined
  }

  private generateBasicTags(domain: string): string[] {
    const tags: string[] = []
    const lowerDomain = domain.toLowerCase()
    
    // 基于域名特征生成标签
    const tagMappings = {
      'app': 'Mobile App',
      'api': 'API',
      'saas': 'SaaS',
      'tool': 'Tool',
      'platform': 'Platform',
      'service': 'Service',
      'cloud': 'Cloud',
      'web': 'Web App',
      'mobile': 'Mobile',
      'enterprise': 'Enterprise',
      'startup': 'Startup',
      'free': 'Free',
      'pro': 'Professional',
      'beta': 'Beta',
      'new': 'New'
    }

    for (const [keyword, tag] of Object.entries(tagMappings)) {
      if (lowerDomain.includes(keyword)) {
        tags.push(tag)
      }
    }

    // 添加一些通用标签
    if (tags.length === 0) {
      tags.push('Web App', 'SaaS')
    }

    return tags
  }
}
