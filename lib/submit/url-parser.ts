import { ParserFactory } from '@/lib/parsers'

// 重新导出类型以保持向后兼容
export type { ParsedProductInfo } from '@/lib/parsers/types'

export function validateUrl(url: string): boolean {
  try {
    const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`)
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
  } catch {
    return false
  }
}

export function normalizeUrl(url: string): string {
  if (!url) return ''

  // Remove trailing slash
  url = url.replace(/\/$/, '')

  // Add https if no protocol
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    url = `https://${url}`
  }

  return url
}

export async function parseProductFromUrl(url: string) {
  try {
    const normalizedUrl = normalizeUrl(url)

    // 使用解析器工厂获取为此URL优化的解析器
    const parser = ParserFactory.getOptimizedInstance(normalizedUrl)
    const result = await parser.parse(normalizedUrl)

    if (result.success && result.data) {
      console.log(`Successfully parsed with ${result.source} in ${result.duration}ms`)
      return result.data
    } else {
      console.warn('Auto parser failed:', result.error)
      // 回退到基础解析
      return generateBasicInfo(normalizedUrl)
    }
  } catch (error) {
    console.error('Error parsing product from URL:', error)
    // 回退到基础解析
    return generateBasicInfo(url)
  }
}

// 基础信息生成函数（作为最后的回退）
function generateBasicInfo(url: string) {
  try {
    const normalizedUrl = normalizeUrl(url)
    const urlObj = new URL(normalizedUrl)
    const domain = urlObj.hostname.replace('www.', '')

    // Extract potential product name from domain
    const domainParts = domain.split('.')
    const potentialName = domainParts[0]
      .split('-')
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join(' ')

    return {
      name: potentialName,
      tagline: `Discover ${potentialName}`,
      description: `Learn more about ${potentialName} and what makes it special.`,
    }
  } catch (error) {
    return {
      name: 'Unknown Product',
      tagline: 'A new product to discover',
      description: 'This product is waiting to be explored.'
    }
  }
}

export function extractDomainFromUrl(url: string): string {
  try {
    const normalizedUrl = normalizeUrl(url)
    const urlObj = new URL(normalizedUrl)
    return urlObj.hostname.replace('www.', '')
  } catch {
    return ''
  }
}
