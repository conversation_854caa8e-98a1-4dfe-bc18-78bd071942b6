export interface ParsedProductInfo {
  name?: string
  tagline?: string
  description?: string
  logoUrl?: string
  coverImageUrl?: string
}

export function validateUrl(url: string): boolean {
  try {
    const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`)
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
  } catch {
    return false
  }
}

export function normalizeUrl(url: string): string {
  if (!url) return ''
  
  // Remove trailing slash
  url = url.replace(/\/$/, '')
  
  // Add https if no protocol
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    url = `https://${url}`
  }
  
  return url
}

export async function parseProductFromUrl(url: string): Promise<ParsedProductInfo> {
  try {
    const normalizedUrl = normalizeUrl(url)
    
    // For now, we'll return basic info extracted from URL
    // In the future, this could be enhanced with web scraping
    const urlObj = new URL(normalizedUrl)
    const domain = urlObj.hostname.replace('www.', '')
    
    // Extract potential product name from domain
    const domainParts = domain.split('.')
    const potentialName = domainParts[0]
      .split('-')
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join(' ')
    
    return {
      name: potentialName,
      tagline: `Discover ${potentialName}`,
      description: `Learn more about ${potentialName} and what makes it special.`,
    }
  } catch (error) {
    console.error('Error parsing product from URL:', error)
    return {}
  }
}

export function extractDomainFromUrl(url: string): string {
  try {
    const normalizedUrl = normalizeUrl(url)
    const urlObj = new URL(normalizedUrl)
    return urlObj.hostname.replace('www.', '')
  } catch {
    return ''
  }
}
