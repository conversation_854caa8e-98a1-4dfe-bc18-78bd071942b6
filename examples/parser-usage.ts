/**
 * 解析器使用示例
 * 展示如何在不同场景下使用自动解析器系统
 */

import { 
  ParserFactory, 
  AutoParser, 
  ParseStrategy,
  createFastParser,
  createHighQualityParser,
  PARSER_PRESETS
} from '@/lib/parsers'

// 示例1: 基础使用 - 使用工厂方法
async function basicUsage() {
  console.log('=== 基础使用示例 ===')
  
  // 获取默认解析器实例
  const parser = ParserFactory.getInstance('default')
  
  const urls = [
    'https://github.com/vercel/next.js',
    'https://supabase.com',
    'https://openai.com'
  ]
  
  for (const url of urls) {
    console.log(`\n解析: ${url}`)
    const result = await parser.parse(url)
    
    if (result.success) {
      console.log(`✅ 成功 (${result.source}, ${result.duration}ms)`)
      console.log(`名称: ${result.data?.name}`)
      console.log(`标语: ${result.data?.tagline}`)
      console.log(`分类: ${result.data?.category}`)
    } else {
      console.log(`❌ 失败: ${result.error}`)
    }
  }
}

// 示例2: 针对特定URL类型优化
async function optimizedUsage() {
  console.log('\n=== 优化使用示例 ===')
  
  const testCases = [
    {
      url: 'https://github.com/microsoft/vscode',
      description: 'GitHub仓库 - 应优先使用Jina'
    },
    {
      url: 'https://linear.app',
      description: 'SaaS产品 - 应优先使用Firecrawl'
    },
    {
      url: 'https://docs.nextjs.org',
      description: '文档站点 - 应优先使用Jina'
    }
  ]
  
  for (const testCase of testCases) {
    console.log(`\n${testCase.description}`)
    console.log(`URL: ${testCase.url}`)
    
    // 获取为此URL优化的解析器
    const parser = ParserFactory.getOptimizedInstance(testCase.url)
    const result = await parser.parse(testCase.url)
    
    if (result.success) {
      console.log(`✅ 解析器: ${result.source}`)
      console.log(`产品: ${result.data?.name}`)
      console.log(`标签: ${result.data?.tags?.join(', ') || '无'}`)
    }
  }
}

// 示例3: 不同策略比较
async function strategyComparison() {
  console.log('\n=== 策略比较示例 ===')
  
  const url = 'https://vercel.com'
  const strategies = [
    { name: 'FAST', parser: createFastParser() },
    { name: 'HIGH_QUALITY', parser: createHighQualityParser() },
    { 
      name: 'PARALLEL', 
      parser: new AutoParser(PARSER_PRESETS.COMPREHENSIVE) 
    }
  ]
  
  console.log(`测试URL: ${url}\n`)
  
  for (const strategy of strategies) {
    console.log(`策略: ${strategy.name}`)
    const startTime = Date.now()
    const result = await strategy.parser.parse(url)
    const totalTime = Date.now() - startTime
    
    if (result.success) {
      console.log(`✅ 成功 (总耗时: ${totalTime}ms)`)
      console.log(`解析器: ${result.source}`)
      console.log(`数据完整性: ${calculateCompleteness(result.data)}%`)
    } else {
      console.log(`❌ 失败: ${result.error}`)
    }
    console.log('---')
  }
}

// 示例4: 自定义配置
async function customConfiguration() {
  console.log('\n=== 自定义配置示例 ===')
  
  // 创建专门针对开发者工具的解析器
  const devToolsParser = new AutoParser({
    strategy: ParseStrategy.FALLBACK,
    timeout: 20000,
    maxRetries: 1,
    enabledParsers: ['jina', 'basic'],
    priorities: [
      {
        parser: 'jina',
        weight: 100,
        conditions: {
          patterns: [/github\.com/, /gitlab\.com/, /\.dev/, /docs\./]
        }
      },
      {
        parser: 'basic',
        weight: 50
      }
    ]
  })
  
  const devUrls = [
    'https://github.com/facebook/react',
    'https://docs.github.com',
    'https://code.visualstudio.com'
  ]
  
  console.log('使用专门的开发者工具解析器:')
  for (const url of devUrls) {
    const result = await devToolsParser.parse(url)
    console.log(`${url} -> ${result.success ? '✅' : '❌'} (${result.source})`)
  }
}

// 示例5: 错误处理和重试
async function errorHandling() {
  console.log('\n=== 错误处理示例 ===')
  
  const problematicUrls = [
    'https://nonexistent-domain-12345.com',
    'invalid-url',
    'https://httpstat.us/500', // 返回500错误的测试URL
  ]
  
  const robustParser = new AutoParser({
    strategy: ParseStrategy.FALLBACK,
    timeout: 5000,
    maxRetries: 2,
    enabledParsers: ['basic'], // 只使用基础解析器确保有结果
    priorities: [
      { parser: 'basic', weight: 100 }
    ]
  })
  
  for (const url of problematicUrls) {
    console.log(`\n测试问题URL: ${url}`)
    try {
      const result = await robustParser.parse(url)
      if (result.success) {
        console.log(`✅ 成功回退到基础解析: ${result.data?.name}`)
      } else {
        console.log(`❌ 解析失败: ${result.error}`)
      }
    } catch (error) {
      console.log(`💥 异常: ${error}`)
    }
  }
}

// 示例6: 性能监控
async function performanceMonitoring() {
  console.log('\n=== 性能监控示例 ===')
  
  const urls = [
    'https://stripe.com',
    'https://github.com/vercel/next.js',
    'https://supabase.com',
    'https://openai.com',
    'https://vercel.com'
  ]
  
  const parser = ParserFactory.getInstance('balanced')
  const results = []
  
  console.log('开始批量解析...')
  const batchStartTime = Date.now()
  
  for (const url of urls) {
    const result = await parser.parse(url)
    results.push({
      url,
      success: result.success,
      source: result.source,
      duration: result.duration,
      dataQuality: result.success ? calculateCompleteness(result.data) : 0
    })
  }
  
  const totalTime = Date.now() - batchStartTime
  
  // 统计报告
  console.log('\n📊 性能报告:')
  console.log(`总耗时: ${totalTime}ms`)
  console.log(`平均耗时: ${Math.round(totalTime / urls.length)}ms`)
  console.log(`成功率: ${Math.round(results.filter(r => r.success).length / results.length * 100)}%`)
  
  // 按解析器分组统计
  const byParser = results.reduce((acc, result) => {
    if (!acc[result.source]) {
      acc[result.source] = { count: 0, totalTime: 0, avgQuality: 0 }
    }
    acc[result.source].count++
    acc[result.source].totalTime += result.duration
    acc[result.source].avgQuality += result.dataQuality
    return acc
  }, {} as Record<string, any>)
  
  console.log('\n解析器使用统计:')
  Object.entries(byParser).forEach(([parser, stats]) => {
    console.log(`${parser}: ${stats.count}次, 平均${Math.round(stats.totalTime / stats.count)}ms, 质量${Math.round(stats.avgQuality / stats.count)}%`)
  })
}

// 辅助函数：计算数据完整性
function calculateCompleteness(data: any): number {
  if (!data) return 0
  
  const fields = ['name', 'tagline', 'description', 'logoUrl', 'coverImageUrl', 'category', 'tags']
  const filledFields = fields.filter(field => {
    const value = data[field]
    return value && (typeof value === 'string' ? value.length > 0 : Array.isArray(value) ? value.length > 0 : true)
  })
  
  return Math.round((filledFields.length / fields.length) * 100)
}

// 主函数：运行所有示例
async function runAllExamples() {
  console.log('🚀 自动解析器系统示例\n')
  
  try {
    await basicUsage()
    await optimizedUsage()
    await strategyComparison()
    await customConfiguration()
    await errorHandling()
    await performanceMonitoring()
    
    console.log('\n✅ 所有示例运行完成!')
  } catch (error) {
    console.error('❌ 示例运行出错:', error)
  }
}

// 如果直接运行此文件
if (require.main === module) {
  runAllExamples()
}

export {
  basicUsage,
  optimizedUsage,
  strategyComparison,
  customConfiguration,
  errorHandling,
  performanceMonitoring,
  runAllExamples
}
