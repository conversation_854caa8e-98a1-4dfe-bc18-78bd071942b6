# 自动解析器系统文档

## 概述

自动解析器系统是一个强大的产品信息提取工具，支持多种解析方案，能够从产品URL自动提取详细的产品信息。

## 解析器类型

### 1. Jina AI Parser
- **适用场景**: 开发者工具、GitHub仓库、文档站点
- **特点**: 快速、准确的文本提取
- **API**: Jina AI Reader API
- **配置**: 需要 `JINA_API_KEY` 环境变量

### 2. Firecrawl Parser
- **适用场景**: SaaS产品、复杂网站、需要结构化数据提取
- **特点**: 支持LLM增强提取，能获取更详细的信息
- **API**: Firecrawl API
- **配置**: 需要 `FIRECRAWL_API_KEY` 环境变量

### 3. Basic Parser
- **适用场景**: 备用方案、快速响应
- **特点**: 无需API，基于URL生成基础信息
- **配置**: 无需配置，总是可用

## 解析策略

### FIRST_SUCCESS (首次成功)
按优先级顺序尝试解析器，返回第一个成功的结果。
- **优点**: 快速响应
- **适用**: 对速度要求高的场景

### BEST_RESULT (最佳结果)
并行运行所有解析器，选择质量最高的结果。
- **优点**: 结果质量高
- **适用**: 对准确性要求高的场景

### PARALLEL (并行合并)
并行运行所有解析器，合并所有成功的结果。
- **优点**: 信息最全面
- **适用**: 需要详细信息的场景

### FALLBACK (回退策略)
按优先级依次尝试，支持重试机制。
- **优点**: 平衡速度和质量
- **适用**: 大多数场景（推荐）

## 使用方法

### 基础使用
```typescript
import { parseProductFromUrl } from '@/lib/submit/url-parser'

const productInfo = await parseProductFromUrl('https://example.com')
console.log(productInfo)
```

### 高级使用
```typescript
import { ParserFactory, createHighQualityParser } from '@/lib/parsers'

// 使用工厂方法
const parser = ParserFactory.getInstance('high-quality')
const result = await parser.parse('https://example.com')

// 创建自定义解析器
const customParser = createHighQualityParser()
const result2 = await customParser.parse('https://example.com')
```

### 自定义配置
```typescript
import { AutoParser, ParseStrategy } from '@/lib/parsers'

const parser = new AutoParser({
  strategy: ParseStrategy.FALLBACK,
  timeout: 30000,
  maxRetries: 2,
  enabledParsers: ['firecrawl', 'jina', 'basic'],
  priorities: [
    {
      parser: 'firecrawl',
      weight: 100,
      conditions: {
        domains: ['.app', '.io']
      }
    },
    {
      parser: 'jina',
      weight: 80,
      conditions: {
        patterns: [/github\.com/]
      }
    },
    {
      parser: 'basic',
      weight: 10
    }
  ]
})
```

## 配置选项

### 环境变量
```bash
# API密钥
JINA_API_KEY=your_jina_api_key
FIRECRAWL_API_KEY=your_firecrawl_api_key

# 可选配置
PARSER_TIMEOUT=30000
PARSER_MAX_RETRIES=2
PARSER_STRATEGY=fallback
ENABLED_PARSERS=firecrawl,jina,basic
```

### 预设配置
- `FAST`: 快速模式，优先使用基础解析器
- `BALANCED`: 平衡模式，速度和质量兼顾
- `HIGH_QUALITY`: 高质量模式，优先详细信息
- `COMPREHENSIVE`: 综合模式，并行合并结果

## 解析结果

### 返回数据结构
```typescript
interface ParsedProductInfo {
  name?: string           // 产品名称
  tagline?: string        // 产品标语
  description?: string    // 产品描述
  logoUrl?: string        // Logo URL
  coverImageUrl?: string  // 封面图片 URL
  category?: string       // 产品分类
  tags?: string[]         // 标签列表
  price?: string          // 价格信息
  features?: string[]     // 功能特性
  screenshots?: string[]  // 截图列表
}
```

### 解析结果
```typescript
interface ParseResult {
  success: boolean        // 是否成功
  data?: ParsedProductInfo // 解析的数据
  error?: string          // 错误信息
  source: string          // 解析器名称
  duration: number        // 解析耗时（毫秒）
}
```

## 智能路由

系统会根据URL特征自动选择最适合的解析器：

### GitHub/GitLab
- **优先**: Jina Parser
- **原因**: 擅长处理代码仓库和文档

### SaaS产品 (.app, .io域名)
- **优先**: Firecrawl Parser
- **原因**: 能更好地提取产品营销信息

### 文档站点 (docs.*)
- **优先**: Jina Parser
- **原因**: 文本提取能力强

### 其他网站
- **策略**: 平衡配置，按权重依次尝试

## 性能优化

### 缓存策略
- 解析器实例缓存
- 按域名优化的实例缓存
- 结果缓存（可选）

### 超时控制
- 单个解析器超时: 30秒
- 总体解析超时: 45秒
- 重试机制: 最多2次

### 错误处理
- 优雅降级到基础解析器
- 详细的错误日志
- 解析时间统计

## 扩展解析器

### 添加新解析器
1. 实现 `ProductParser` 接口
2. 在 `AutoParser` 中注册
3. 配置优先级和条件

```typescript
class CustomParser implements ProductParser {
  name = 'custom'
  
  isAvailable(): boolean {
    return true
  }
  
  async parse(url: string, config?: ParserConfig): Promise<ParseResult> {
    // 实现解析逻辑
  }
}
```

## 最佳实践

1. **API密钥管理**: 使用环境变量存储API密钥
2. **错误处理**: 总是处理解析失败的情况
3. **性能监控**: 记录解析时间和成功率
4. **配置调优**: 根据实际使用情况调整优先级
5. **缓存策略**: 对频繁访问的域名启用缓存

## 故障排除

### 常见问题
1. **API密钥无效**: 检查环境变量配置
2. **解析超时**: 调整超时时间或使用快速模式
3. **结果质量差**: 尝试高质量模式或并行模式
4. **特定网站失败**: 为该域名配置专门的解析器优先级

### 调试技巧
- 启用详细日志
- 检查解析器可用性
- 测试单个解析器
- 验证URL格式
